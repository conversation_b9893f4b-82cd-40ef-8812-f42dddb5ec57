using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class DriverSelectionWindow : Window
    {
        private readonly IDataService _dataService;
        private List<Driver> _allDrivers;
        private List<Driver> _filteredDrivers;

        public Driver? SelectedDriver { get; private set; }
        public string SearchText { get; set; } = string.Empty;

        public DriverSelectionWindow()
        {
            InitializeComponent();
            _dataService = new DataService();
            LoadDrivers();
        }

        private async void LoadDrivers()
        {
            try
            {
                _allDrivers = (await _dataService.GetDriversAsync()).Where(d => d.IsActive).ToList();
                _filteredDrivers = new List<Driver>(_allDrivers);
                DriversDataGrid.ItemsSource = _filteredDrivers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل السائقين: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterDrivers();
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            FilterDrivers();
        }

        private void FilterDrivers()
        {
            if (_allDrivers == null) return;

            var searchText = SearchTextBox.Text?.Trim().ToLower() ?? string.Empty;

            if (string.IsNullOrEmpty(searchText))
            {
                _filteredDrivers = new List<Driver>(_allDrivers);
            }
            else
            {
                _filteredDrivers = _allDrivers.Where(d =>
                    (!string.IsNullOrEmpty(d.Name) && d.Name.ToLower().Contains(searchText)) ||
                    (!string.IsNullOrEmpty(d.PhoneNumber) && d.PhoneNumber.Contains(searchText)) ||
                    (!string.IsNullOrEmpty(d.CardNumber) && d.CardNumber.Contains(searchText)) ||
                    (!string.IsNullOrEmpty(d.DriverCode) && d.DriverCode.ToLower().Contains(searchText)) ||
                    (!string.IsNullOrEmpty(d.VehicleType) && d.VehicleType.ToLower().Contains(searchText)) ||
                    (!string.IsNullOrEmpty(d.VehicleNumber) && d.VehicleNumber.ToLower().Contains(searchText))
                ).ToList();
            }

            DriversDataGrid.ItemsSource = _filteredDrivers;
        }

        private void DriversDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            SelectButton.IsEnabled = DriversDataGrid.SelectedItem != null;
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            if (DriversDataGrid.SelectedItem is Driver selectedDriver)
            {
                SelectedDriver = selectedDriver;
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("يرجى اختيار سائق من القائمة", "تنبيه", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
