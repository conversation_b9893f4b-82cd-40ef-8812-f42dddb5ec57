<Window x:Class="DriverManagementSystem.Views.DriverSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار سائق"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Window.Resources>
        <!-- تعريف الأنماط -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="SearchBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="اختيار سائق لإضافته إلى قائمة المفاضلة" 
                   Style="{StaticResource HeaderTextStyle}"/>

        <!-- مربع البحث -->
        <Grid Grid.Row="1" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBox x:Name="SearchTextBox" Grid.Column="0"
                     Style="{StaticResource SearchBoxStyle}"
                     TextChanged="SearchTextBox_TextChanged"
                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                     Tag="البحث بالاسم أو رقم الهوية أو رقم الهاتف..."/>
            
            <Button Grid.Column="1" Content="🔍 بحث" 
                    Style="{StaticResource ActionButtonStyle}"
                    Background="#3498DB" Margin="10,0,0,0"
                    Click="SearchButton_Click"/>
        </Grid>

        <!-- قائمة السائقين -->
        <Border Grid.Row="2" BorderBrush="#BDC3C7" BorderThickness="1" Background="White">
            <DataGrid x:Name="DriversDataGrid"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      AlternatingRowBackground="#F8F9FA"
                      RowBackground="White"
                      SelectionChanged="DriversDataGrid_SelectionChanged">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الكود" Binding="{Binding DriverCode}" Width="80" IsReadOnly="True"/>
                    <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="200" IsReadOnly="True"/>
                    <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding PhoneNumber}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="رقم الهوية" Binding="{Binding CardNumber}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="نوع المركبة" Binding="{Binding VehicleType}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="رقم المركبة" Binding="{Binding VehicleNumber}" Width="100" IsReadOnly="True"/>
                    <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60" IsReadOnly="True"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- أزرار العمليات -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,15,0,0">
            
            <Button x:Name="SelectButton" Content="✅ اختيار" 
                    Style="{StaticResource ActionButtonStyle}"
                    Background="#27AE60"
                    Click="SelectButton_Click"
                    IsEnabled="False"/>
            
            <Button Content="❌ إلغاء" 
                    Style="{StaticResource ActionButtonStyle}"
                    Background="#E74C3C"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
